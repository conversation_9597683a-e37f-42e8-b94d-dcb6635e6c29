import { Handlers } from "$fresh/server.ts";
import { State } from "../../_middleware.ts";
import { TaskService } from "../../../lib/kv.ts";
import { MonitorTask, CreateTaskRequest } from "../../../types/index.ts";
import { generateId } from "../../../lib/auth.ts";
import { isValidUrl } from "../../../lib/utils.ts";
import { createSuccessResponse, createErrorResponse } from "../../../lib/utils.ts";
import { MonitorService } from "../../../lib/monitor.ts";

export const handler: Handlers<unknown, State> = {
  // 获取用户的所有任务
  async GET(req, ctx) {
    if (!ctx.state.user) {
      return new Response(
        JSON.stringify(createErrorResponse("未授权访问")),
        { status: 401, headers: { "Content-Type": "application/json" } }
      );
    }

    try {
      const tasks = await TaskService.getTasksByUser(ctx.state.user.id);
      
      // 为每个任务添加统计信息
      const tasksWithStats = await Promise.all(
        tasks.map(async (task) => {
          const stats = await MonitorService.getTaskStats(task.id);
          return {
            ...task,
            stats,
          };
        })
      );

      return new Response(
        JSON.stringify(createSuccessResponse(tasksWithStats)),
        { headers: { "Content-Type": "application/json" } }
      );
    } catch (error) {
      console.error("获取任务列表失败:", error);
      return new Response(
        JSON.stringify(createErrorResponse("获取任务列表失败")),
        { status: 500, headers: { "Content-Type": "application/json" } }
      );
    }
  },

  // 创建新任务
  async POST(req, ctx) {
    if (!ctx.state.user) {
      return new Response(
        JSON.stringify(createErrorResponse("未授权访问")),
        { status: 401, headers: { "Content-Type": "application/json" } }
      );
    }

    try {
      const body: CreateTaskRequest = await req.json();

      // 验证输入
      if (!body.name || !body.url) {
        return new Response(
          JSON.stringify(createErrorResponse("任务名称和URL不能为空")),
          { status: 400, headers: { "Content-Type": "application/json" } }
        );
      }

      if (!isValidUrl(body.url)) {
        return new Response(
          JSON.stringify(createErrorResponse("URL格式不正确")),
          { status: 400, headers: { "Content-Type": "application/json" } }
        );
      }

      if (body.intervalMinutes < 1 || body.intervalMinutes > 1440) {
        return new Response(
          JSON.stringify(createErrorResponse("监控间隔必须在1-1440分钟之间")),
          { status: 400, headers: { "Content-Type": "application/json" } }
        );
      }

      if (body.randomRangeMinutes < 0 || body.randomRangeMinutes > 60) {
        return new Response(
          JSON.stringify(createErrorResponse("随机范围必须在0-60分钟之间")),
          { status: 400, headers: { "Content-Type": "application/json" } }
        );
      }

      // 检查用户任务数量限制
      const existingTasks = await TaskService.getTasksByUser(ctx.state.user.id);
      if (existingTasks.length >= 10) {
        return new Response(
          JSON.stringify(createErrorResponse("每个用户最多只能创建10个监控任务")),
          { status: 400, headers: { "Content-Type": "application/json" } }
        );
      }

      // 创建任务
      const now = new Date();

      // 计算下次执行时间，包含随机范围
      const baseInterval = body.intervalMinutes * 60 * 1000; // 转换为毫秒
      let randomOffset = 0;
      if (body.randomRangeMinutes > 0) {
        randomOffset = Math.random() * body.randomRangeMinutes * 60 * 1000;
      }
      const nextRunTime = new Date(now.getTime() + baseInterval + randomOffset);

      const task: MonitorTask = {
        id: generateId(),
        userId: ctx.state.user.id,
        name: body.name,
        url: body.url,
        method: body.method || "GET",
        headers: body.headers || undefined,
        body: body.body || undefined,
        useCookie: body.useCookie || false,
        cookies: body.cookies || undefined,
        intervalMinutes: body.intervalMinutes,
        randomRangeMinutes: body.randomRangeMinutes,
        isActive: true,
        createdAt: now,
        updatedAt: now,
        lastRunAt: undefined,
        nextRunAt: nextRunTime,
      };

      console.log("开始保存任务到数据库");
      await TaskService.create(task);
      console.log("任务创建成功:", task.id);

      return new Response(
        JSON.stringify(createSuccessResponse(task, "任务创建成功")),
        { status: 201, headers: { "Content-Type": "application/json" } }
      );
    } catch (error) {
      console.error("创建任务失败:", error);
      console.error("错误详情:", error instanceof Error ? error.stack : error);
      return new Response(
        JSON.stringify(createErrorResponse(`创建任务失败：${error instanceof Error ? error.message : String(error)}`)),
        { status: 500, headers: { "Content-Type": "application/json" } }
      );
    }
  },
};
