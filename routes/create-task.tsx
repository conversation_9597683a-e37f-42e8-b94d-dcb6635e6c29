import { Head } from "$fresh/runtime.ts";
import { Handlers, PageProps } from "$fresh/server.ts";
import { State } from "./_middleware.ts";
import { TaskService } from "../lib/kv.ts";
import { MonitorTask, CreateTaskRequest } from "../types/index.ts";
import { generateId } from "../lib/auth.ts";
import { isValidUrl } from "../lib/utils.ts";
import { MonitorService } from "../lib/monitor.ts";

interface CreateTaskData {
  error?: string;
  success?: string;
}

export const handler: Handlers<CreateTaskData, State> = {
  GET(req, ctx) {
    // 检查用户是否已登录
    if (!ctx.state.user) {
      return new Response("", {
        status: 302,
        headers: { Location: "/login" },
      });
    }

    return ctx.render({});
  },

  async POST(req, ctx) {
    // 检查用户是否已登录
    if (!ctx.state.user) {
      return new Response("", {
        status: 302,
        headers: { Location: "/login" },
      });
    }

    try {
      console.log("开始处理创建任务请求");

      const form = await req.formData();
      const name = form.get("name") as string;
      const url = form.get("url") as string;
      const method = form.get("method") as string;
      const intervalMinutes = parseInt(form.get("intervalMinutes") as string);
      const randomRangeMinutes = parseInt(form.get("randomRangeMinutes") as string);
      const useCookie = form.has("useCookie");

      console.log("表单数据:", { name, url, method, intervalMinutes, randomRangeMinutes, useCookie });

      // 验证输入
      if (!name || !url) {
        console.log("验证失败: 任务名称和URL不能为空");
        return ctx.render({ error: "任务名称和URL不能为空" });
      }

      if (!isValidUrl(url)) {
        console.log("验证失败: URL格式不正确");
        return ctx.render({ error: "URL格式不正确" });
      }

      if (intervalMinutes < 1 || intervalMinutes > 1440) {
        console.log("验证失败: 监控间隔超出范围");
        return ctx.render({ error: "监控间隔必须在1-1440分钟之间" });
      }

      if (randomRangeMinutes < 0 || randomRangeMinutes > 60) {
        console.log("验证失败: 随机范围超出范围");
        return ctx.render({ error: "随机范围必须在0-60分钟之间" });
      }

      // 检查用户任务数量限制
      console.log("检查用户任务数量限制");
      const existingTasks = await TaskService.getTasksByUser(ctx.state.user.id);
      console.log("现有任务数量:", existingTasks.length);
      if (existingTasks.length >= 10) {
        console.log("验证失败: 任务数量超出限制");
        return ctx.render({ error: "每个用户最多只能创建10个监控任务" });
      }

      // 创建任务
      console.log("开始创建任务");
      const now = new Date();
      const nextRunTime = new Date(Date.now() + intervalMinutes * 60 * 1000);

      const task: MonitorTask = {
        id: generateId(),
        userId: ctx.state.user.id,
        name: name.trim(),
        url: url.trim(),
        method: method as 'GET' | 'POST',
        useCookie,
        intervalMinutes,
        randomRangeMinutes,
        isActive: true,
        createdAt: now,
        updatedAt: now,
        nextRunAt: nextRunTime,
      };

      console.log("任务对象:", task);

      await TaskService.create(task);
      console.log("任务创建成功，准备重定向");

      // 重定向到仪表板
      return new Response("", {
        status: 302,
        headers: { Location: "/dashboard?success=任务创建成功" },
      });
    } catch (error) {
      console.error("创建任务失败:", error);
      console.error("错误详情:", error instanceof Error ? error.stack : error);
      return ctx.render({ error: `创建任务失败：${error instanceof Error ? error.message : String(error)}` });
    }
  },
};

export default function CreateTaskPage({ data }: PageProps<CreateTaskData>) {
  return (
    <>
      <Head>
        <title>创建监控任务 - 网站监控系统</title>
      </Head>

      <div class="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
        <div class="sm:mx-auto sm:w-full sm:max-w-md">
          <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
            创建监控任务
          </h2>
          <p class="mt-2 text-center text-sm text-gray-600">
            <a href="/dashboard" class="font-medium text-blue-600 hover:text-blue-500">
              返回仪表板
            </a>
          </p>
        </div>

        <div class="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
          <div class="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
            {data?.error && (
              <div class="mb-4 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
                {data.error}
              </div>
            )}

            <form class="space-y-6" method="POST">
              <div>
                <label htmlFor="name" class="block text-sm font-medium text-gray-700">
                  任务名称
                </label>
                <div class="mt-1">
                  <input
                    id="name"
                    name="name"
                    type="text"
                    required
                    class="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    placeholder="例如：百度监控"
                  />
                </div>
              </div>

              <div>
                <label htmlFor="url" class="block text-sm font-medium text-gray-700">
                  监控URL
                </label>
                <div class="mt-1">
                  <input
                    id="url"
                    name="url"
                    type="url"
                    required
                    class="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    placeholder="https://example.com"
                  />
                </div>
              </div>

              <div>
                <label htmlFor="method" class="block text-sm font-medium text-gray-700">
                  请求方法
                </label>
                <div class="mt-1">
                  <select
                    id="method"
                    name="method"
                    class="block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  >
                    <option value="GET">GET</option>
                    <option value="POST">POST</option>
                  </select>
                </div>
              </div>

              <div>
                <label htmlFor="intervalMinutes" class="block text-sm font-medium text-gray-700">
                  监控间隔（分钟）
                </label>
                <div class="mt-1">
                  <input
                    id="intervalMinutes"
                    name="intervalMinutes"
                    type="number"
                    min="1"
                    max="1440"
                    value="5"
                    required
                    class="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  />
                </div>
              </div>

              <div>
                <label htmlFor="randomRangeMinutes" class="block text-sm font-medium text-gray-700">
                  随机范围（分钟）
                </label>
                <div class="mt-1">
                  <input
                    id="randomRangeMinutes"
                    name="randomRangeMinutes"
                    type="number"
                    min="0"
                    max="60"
                    value="1"
                    class="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  />
                </div>
              </div>

              <div class="flex items-center">
                <input
                  id="useCookie"
                  name="useCookie"
                  type="checkbox"
                  class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label htmlFor="useCookie" class="ml-2 block text-sm text-gray-900">
                  使用 Cookie
                </label>
              </div>

              <div>
                <button
                  type="submit"
                  class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  创建任务
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </>
  );
}
