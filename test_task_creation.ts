#!/usr/bin/env -S deno run --allow-net --allow-env

// 测试任务创建功能
async function testTaskCreation() {
  const baseUrl = "http://localhost:8000"; // 本地测试
  
  console.log("🧪 开始测试任务创建功能...");
  
  // 测试数据
  const testTask = {
    name: "测试任务",
    url: "https://www.baidu.com",
    method: "GET",
    intervalMinutes: 5,
    randomRangeMinutes: 2,
    useCookie: false
  };
  
  try {
    // 测试API端点
    console.log("📡 测试API端点 /api/tasks");
    const response = await fetch(`${baseUrl}/api/tasks`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        // 这里需要实际的JWT token，暂时跳过认证测试
      },
      body: JSON.stringify(testTask)
    });
    
    const result = await response.json();
    console.log("API响应:", result);
    
    if (response.ok && result.success) {
      console.log("✅ API端点测试成功");
    } else {
      console.log("❌ API端点测试失败:", result.error);
    }
    
  } catch (error) {
    console.log("❌ 测试过程中出现错误:", error.message);
  }
}

// 运行测试
if (import.meta.main) {
  await testTaskCreation();
}
