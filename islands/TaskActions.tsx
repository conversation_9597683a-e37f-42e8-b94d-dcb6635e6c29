import { useState } from "preact/hooks";

interface TaskActionsProps {
  taskId: string;
}

export default function TaskActions({ taskId }: TaskActionsProps) {
  const [isExecuting, setIsExecuting] = useState(false);

  const executeTask = async () => {
    if (isExecuting) return;
    
    setIsExecuting(true);
    try {
      const response = await fetch(`/api/tasks/${taskId}/execute`, {
        method: 'POST'
      });
      const result = await response.json();
      
      if (result.success) {
        alert('任务执行成功');
        // 刷新页面以显示最新结果
        window.location.reload();
      } else {
        alert('任务执行失败: ' + result.error);
      }
    } catch (error) {
      alert('执行失败: ' + (error instanceof Error ? error.message : String(error)));
    } finally {
      setIsExecuting(false);
    }
  };

  const editTask = () => {
    alert('编辑任务功能需要在前端实现: ' + taskId);
  };

  return (
    <div class="flex space-x-2">
      <button
        onClick={executeTask}
        disabled={isExecuting}
        class={`text-sm ${
          isExecuting 
            ? 'text-gray-400 cursor-not-allowed' 
            : 'text-blue-600 hover:text-blue-900 cursor-pointer'
        }`}
      >
        {isExecuting ? '执行中...' : '执行'}
      </button>
      <button
        onClick={editTask}
        class="text-gray-600 hover:text-gray-900 text-sm cursor-pointer"
      >
        编辑
      </button>
    </div>
  );
}
